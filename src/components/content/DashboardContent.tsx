"use client";

import React, { useState } from "react";
import { useProfile } from "@/hooks/useProfile";
import TaskDetailModal from "@/components/TaskDetailModal";

import { creatorKitTasks } from "@/data/creator-kit-tasks";
import SidebarContent from "@/components/SidebarContent";

const DashboardContent: React.FC = () => {
  const { profile } = useProfile();
  const [activeTaskId, setActiveTaskId] = useState<string | null>(null);

  const handleTaskClick = (taskId: string) => {
    setActiveTaskId(taskId);
  };

  const activeTask = activeTaskId
    ? creatorKitTasks.find((task) => task.id === activeTaskId)
    : null;

  return (
    <div className="flex flex-col gap-10 animate-fade-in">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-2 h-9 rounded-full bg-gradient-to-b from-[#4ecdc4] to-[#f39c12] animate-pulse" />
          <h1 className="text-white font-heading text-3xl font-bold tracking-wide">
            創作者中心
          </h1>
        </div>
      </div>

      {/* Migrated sidebar content - full width */}
      <div className="w-full">
        <SidebarContent className="w-full" />
      </div>

      {/* Task Detail Modal */}
      {activeTask && (
        <TaskDetailModal
          task={{
            id: activeTask.id,
            title: activeTask.title,
            description: activeTask.description,
            reward: activeTask.reward,
            tag: activeTask.category,
            timeLeft: `${
              activeTask.totalSteps - activeTask.progress
            } steps left`,
            cta: activeTask.isCompleted ? "Completed" : "Start Task",
          }}
          onClose={() => setActiveTaskId(null)}
        />
      )}
    </div>
  );
};

export default DashboardContent;
