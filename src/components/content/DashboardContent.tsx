"use client";

import React, { useState, useEffect } from "react";
import { useProfile, type UserProfile } from "@/hooks/useProfile";
import TaskDetailModal from "@/components/TaskDetailModal";
import ProfileHeader from "@/components/ProfileHeader";
import ChannelSection from "@/components/ChannelSection";

import { creatorKitTasks } from "@/data/creator-kit-tasks";
import SidebarContent from "@/components/SidebarContent";

interface YouTubeData {
  channelId?: string;
  channelTitle?: string;
  subscriberCount?: number;
  videoCount?: number;
}

const DashboardContent: React.FC = () => {
  const { profile } = useProfile();
  const [activeTaskId, setActiveTaskId] = useState<string | null>(null);
  const [youtubeData, setYoutubeData] = useState<YouTubeData | null>(null);

  useEffect(() => {
    if (profile) {
      // Mock YouTube data - in real app this would come from API
      setYoutubeData({
        channelId: profile.youtube_id || undefined,
        channelTitle: profile.youtube_title || undefined,
        subscriberCount: 0,
        videoCount: 0,
      });
    }
  }, [profile]);

  const handleTaskClick = (taskId: string) => {
    setActiveTaskId(taskId);
  };

  const handleProfileUpdate = (updatedProfile: Partial<UserProfile>) => {
    console.log("Profile updated:", updatedProfile);
    // Handle profile updates - this would typically call the updateProfile function from useProfile
  };

  const activeTask = activeTaskId
    ? creatorKitTasks.find((task) => task.id === activeTaskId)
    : null;

  return (
    <div className="flex flex-col gap-10 animate-fade-in">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-2 h-9 rounded-full bg-gradient-to-b from-[#4ecdc4] to-[#f39c12] animate-pulse" />
          <h1 className="text-white font-heading text-3xl font-bold tracking-wide">
            創作者中心
          </h1>
        </div>
      </div>

      {/* Personal Information Section */}
      {profile && (
        <ProfileHeader
          userProfile={profile}
          onProfileUpdate={handleProfileUpdate}
          className="mb-6"
          editable={true}
        />
      )}

      {/* YouTube Channel Section */}
      {youtubeData?.channelId ? (
        <ChannelSection channelId={youtubeData.channelId} />
      ) : (
        // Demo: Show a public channel when no user channel is connected
        <ChannelSection handle="@MrBeast" />
      )}

      {/* Migrated sidebar content - full width */}
      <div className="w-full">
        <SidebarContent className="w-full" />
      </div>

      {/* Task Detail Modal */}
      {activeTask && (
        <TaskDetailModal
          task={{
            id: activeTask.id,
            title: activeTask.title,
            description: activeTask.description,
            reward: activeTask.reward,
            tag: activeTask.category,
            timeLeft: `${
              activeTask.totalSteps - activeTask.progress
            } steps left`,
            cta: activeTask.isCompleted ? "Completed" : "Start Task",
          }}
          onClose={() => setActiveTaskId(null)}
        />
      )}
    </div>
  );
};

export default DashboardContent;
