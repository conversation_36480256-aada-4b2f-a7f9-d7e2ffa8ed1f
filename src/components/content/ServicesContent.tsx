"use client";

import React from "react";
import { useAuth } from "@/contexts/AuthContext";
import ProfileServicesSection from "@/app/profile/ProfileServicesSection";

const ServicesContent: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="flex flex-col gap-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-2 h-9 rounded-full bg-gradient-to-b from-[#4ecdc4] to-[#f39c12] animate-pulse" />
          <h1 className="text-white font-heading text-3xl font-bold tracking-wide">
            服務管理
          </h1>
        </div>
      </div>

      {/* My Services Section */}
      {user?.id && <ProfileServicesSection creatorId={user.id} />}
    </div>
  );
};

export default ServicesContent;
