"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useProfile, type UserProfile } from "@/hooks/useProfile";
import { motion } from "framer-motion";
import ProfileHeader from "@/components/ProfileHeader";
import AccountConnections from "@/components/AccountConnections";
import ChannelSection from "@/components/ChannelSection";

import { Card } from "@/components/ui/card";
import { User, Settings, Camera, Edit, Wallet, Youtube, DollarSign } from "lucide-react";

interface YouTubeData {
  channelId?: string;
  channelTitle?: string;
  subscriberCount?: number;
  videoCount?: number;
}

const ProfileContent: React.FC = () => {
  const { user } = useAuth();
  const { profile, isLoading } = useProfile();
  const [youtubeData, setYoutubeData] = useState<YouTubeData | null>(null);

  useEffect(() => {
    if (profile) {
      // Mock YouTube data - in real app this would come from API
      setYoutubeData({
        channelId: profile.youtube_id || undefined,
        channelTitle: profile.youtube_title || undefined,
        subscriberCount: 0,
        videoCount: 0,
      });
    }
  }, [profile]);

  const handleProfileUpdate = (updatedProfile: Partial<UserProfile>) => {
    console.log("Profile updated:", updatedProfile);
    // Handle profile updates - this would typically call the updateProfile function from useProfile
  };

  const handleConnectionUpdate = (connectionData: any) => {
    console.log("Connection updated:", connectionData);
    // Handle connection updates
  };

  const handleWithdraw = () => {
    console.log("Withdraw requested");
    // Handle withdrawal logic
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-3 text-[#00ff87]">
          <div className="w-8 h-8 border-2 border-[#00ff87] border-t-transparent rounded-full animate-spin" />
          <span className="text-lg font-medium">載入中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 animate-fade-in">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-2 h-9 rounded-full bg-gradient-to-b from-[#4ecdc4] to-[#f39c12] animate-pulse" />
          <h1 className="text-white font-heading text-3xl font-bold tracking-wide">
            個人資料
          </h1>
        </div>
      </div>

      {/* Character Card */}
      {profile && (
        <ProfileHeader
          userProfile={profile}
          onProfileUpdate={handleProfileUpdate}
          className="mb-2"
          editable={true}
        />
      )}

      {/* Compact Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* RPx Balance Card - Compact */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-gradient-to-br from-[#0f1419] via-[#1a202c] to-[#2d3748] backdrop-blur-sm border border-[#4ecdc4]/20 rounded-xl p-4 relative overflow-hidden shadow-xl hover:shadow-[#4ecdc4]/20 transition-all duration-300 group">
            {/* Simplified Background Effects */}
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(78,205,196,0.08)_0%,transparent_50%)]" />

            <div className="relative">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-[#4ecdc4] to-[#60efff] flex items-center justify-center">
                    <DollarSign className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-bold text-sm">RPx 餘額</h3>
                    <p className="text-[#4ecdc4]/70 text-xs">可用代幣</p>
                  </div>
                </div>
                <div className="text-2xl font-black text-[#4ecdc4] drop-shadow-lg">
                  {0} {/* TODO: Add rpx_balance to profile schema */}
                </div>
              </div>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleWithdraw}
                className="w-full py-2 px-3 bg-gradient-to-r from-[#4ecdc4]/15 to-[#60efff]/15 text-[#4ecdc4] rounded-lg text-xs font-bold hover:from-[#4ecdc4]/25 hover:to-[#60efff]/25 border border-[#4ecdc4]/20 hover:border-[#4ecdc4]/40 transition-all duration-300"
              >
                提現
              </motion.button>
            </div>
          </Card>
        </motion.div>

        {/* Level Card - Compact */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-gradient-to-br from-[#0f1419] via-[#1a202c] to-[#2d3748] backdrop-blur-sm border border-[#60efff]/20 rounded-xl p-4 relative overflow-hidden shadow-xl hover:shadow-[#60efff]/20 transition-all duration-300 group">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_70%,rgba(96,239,255,0.08)_0%,transparent_50%)]" />

            <div className="relative">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-[#60efff] to-[#4ecdc4] flex items-center justify-center">
                    <User className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-bold text-sm">等級</h3>
                    <p className="text-[#60efff]/70 text-xs">創作者等級</p>
                  </div>
                </div>
                <div className="text-2xl font-black text-[#60efff] drop-shadow-lg">
                  Lv.{profile?.level || 1}
                </div>
              </div>
              <div className="text-xs text-white/70 bg-[#2a2a3e]/20 rounded-lg p-2 border border-[#60efff]/10">
                <div className="flex justify-between items-center">
                  <span>經驗值:</span>
                  <span className="font-bold text-[#60efff]">{profile?.experience_points?.toLocaleString() || 0}</span>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Profile Completion Card - Compact */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-gradient-to-br from-[#0f1419] via-[#1a202c] to-[#2d3748] backdrop-blur-sm border border-[#f39c12]/20 rounded-xl p-4 relative overflow-hidden shadow-xl hover:shadow-[#f39c12]/20 transition-all duration-300 group">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(243,156,18,0.08)_0%,transparent_50%)]" />

            <div className="relative">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-[#f39c12] to-[#e67e22] flex items-center justify-center">
                    <span className="text-white text-sm">🏆</span>
                  </div>
                  <div>
                    <h3 className="text-white font-bold text-sm">完成度</h3>
                    <p className="text-[#f39c12]/70 text-xs">資料完整度</p>
                  </div>
                </div>
                <div className="text-2xl font-black text-[#f39c12] drop-shadow-lg">
                  {profile?.profile_completion_percentage || 0}%
                </div>
              </div>
              <div className="w-full bg-[#2a2a3e]/20 rounded-full h-2 border border-[#f39c12]/10">
                <div
                  className="bg-gradient-to-r from-[#f39c12] to-[#e67e22] h-full rounded-full transition-all duration-1000"
                  style={{ width: `${profile?.profile_completion_percentage || 0}%` }}
                />
              </div>
            </div>
          </Card>
        </motion.div>


      </div>



      {/* Account Connections */}
      <div className="relative">
        <AccountConnections
          onConnectionUpdate={handleConnectionUpdate}
          className=""
        />
      </div>

      {/* Channel Section */}
      {youtubeData?.channelId ? (
        <ChannelSection channelId={youtubeData.channelId} />
      ) : (
        // Demo: Show a public channel when no user channel is connected
        <ChannelSection handle="@MrBeast" />
      )}
    </div>
  );
};

export default ProfileContent;
