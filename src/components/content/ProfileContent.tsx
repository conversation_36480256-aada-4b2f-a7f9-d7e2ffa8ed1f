"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useProfile, type UserProfile } from "@/hooks/useProfile";
import { motion } from "framer-motion";
import ChannelSection from "@/components/ChannelSection";

import { Youtube } from "lucide-react";

interface YouTubeData {
  channelId?: string;
  channelTitle?: string;
  subscriberCount?: number;
  videoCount?: number;
}

const ProfileContent: React.FC = () => {
  const { user } = useAuth();
  const { profile, isLoading } = useProfile();
  const [youtubeData, setYoutubeData] = useState<YouTubeData | null>(null);

  useEffect(() => {
    if (profile) {
      // Mock YouTube data - in real app this would come from API
      setYoutubeData({
        channelId: profile.youtube_id || undefined,
        channelTitle: profile.youtube_title || undefined,
        subscriberCount: 0,
        videoCount: 0,
      });
    }
  }, [profile]);

  // Removed profile and connection update handlers as they're no longer needed in this section

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-3 text-[#00ff87]">
          <div className="w-8 h-8 border-2 border-[#00ff87] border-t-transparent rounded-full animate-spin" />
          <span className="text-lg font-medium">載入中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 animate-fade-in">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-2 h-9 rounded-full bg-gradient-to-b from-[#4ecdc4] to-[#f39c12] animate-pulse" />
          <h1 className="text-white font-heading text-3xl font-bold tracking-wide">
            個人資料
          </h1>
        </div>
      </div>

      {/* YouTube Integration Section */}
      <div className="relative">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#ff0000] to-[#cc0000] flex items-center justify-center">
            <Youtube className="w-4 h-4 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-white">YouTube 整合</h3>
            <p className="text-sm text-white/60">管理您的 YouTube 頻道設定</p>
          </div>
        </div>
      </div>

      {/* Channel Section */}
      {youtubeData?.channelId ? (
        <ChannelSection channelId={youtubeData.channelId} />
      ) : (
        // Demo: Show a public channel when no user channel is connected
        <ChannelSection handle="@MrBeast" />
      )}
    </div>
  );
};

export default ProfileContent;
