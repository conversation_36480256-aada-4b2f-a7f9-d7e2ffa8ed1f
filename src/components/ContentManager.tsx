"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";

// Import existing page components
import DashboardContent from "@/components/content/DashboardContent";
import ProfileContent from "@/components/content/ProfileContent";
import LeaderboardContent from "@/components/content/LeaderboardContent";
import ServicesContent from "@/components/content/ServicesContent";
// 临时移除的组件导入：
// import TasksContent from "@/components/content/TasksContent";
// import SupportContent from "@/components/content/SupportContent";
// import FeedbackContent from "@/components/content/FeedbackContent";

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href: string;
  description?: string;
}

interface ContentManagerProps {
  currentPath: string;
  onNavigate?: (item: NavigationItem) => void;
}

const ContentManager: React.FC<ContentManagerProps> = ({
  currentPath,
  onNavigate,
}) => {
  const [currentContent, setCurrentContent] = useState<string>("dashboard");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // Update content based on current path
  useEffect(() => {
    const pathToContent: Record<string, string> = {
      "/dashboard": "dashboard",
      "/profile": "profile",
      "/profile-unified": "profile",
      "/leaderboard": "leaderboard",
      "/leaderboard-unified": "leaderboard",
      "/services": "services",
      // 临时移除的路由：
      // "/tasks": "tasks",
      // "/support": "support",
      // "/feedback": "feedback",
    };

    const contentId = pathToContent[currentPath] || "dashboard";
    setCurrentContent(contentId);
  }, [currentPath]);

  // Listen for navigation events from ThreePanelLayout
  useEffect(() => {
    const handleNavigationEvent = (event: CustomEvent) => {
      const { href, id } = event.detail;
      handleContentChange(id, href);
    };

    // Listen for browser back/forward navigation
    const handlePopState = () => {
      const pathToContent: Record<string, string> = {
        "/dashboard": "dashboard",
        "/profile": "profile",
        "/profile-unified": "profile",
        "/leaderboard": "leaderboard",
        "/leaderboard-unified": "leaderboard",
        "/services": "services",
        // 临时移除的路由：
        // "/tasks": "tasks",
        // "/support": "support",
        // "/feedback": "feedback",
      };

      const contentId = pathToContent[window.location.pathname] || "dashboard";
      setCurrentContent(contentId);
    };

    window.addEventListener('navigate', handleNavigationEvent as EventListener);
    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('navigate', handleNavigationEvent as EventListener);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [currentContent]);

  const handleContentChange = async (contentId: string, href: string) => {
    if (contentId === currentContent) return;

    setIsLoading(true);

    try {
      // Simulate loading delay for smooth transition
      await new Promise(resolve => setTimeout(resolve, 300));

      setCurrentContent(contentId);
      setIsLoading(false);

      // Update URL without full page reload for all dashboard-related routes
      const dashboardRoutes = ['/dashboard', '/profile', '/leaderboard', '/tasks', '/services', '/support'];
      const isDashboardRoute = dashboardRoutes.some(route => href.startsWith(route)) || contentId === 'dashboard';

      if (isDashboardRoute) {
        // Update URL and page title
        window.history.pushState({ contentId }, '', href);

        // Update document title based on content
        const titles: Record<string, string> = {
          dashboard: "創作者中心 - VG蜂巢",
          profile: "個人資料 - VG蜂巢",
          leaderboard: "創作者榜單 - VG蜂巢",
          services: "服務管理 - VG蜂巢",
          // 临时移除的页面标题：
          // tasks: "任務中心 - VG蜂巢",
          // support: "幫助支援 - VG蜂巢",
          // feedback: "意見回饋 - VG蜂巢",
        };
        document.title = titles[contentId] || "VG蜂巢";
      } else {
        // For other routes, use normal navigation
        router.push(href);
      }
    } catch (error) {
      console.error('Content loading error:', error);
      setIsLoading(false);
      // Fallback to normal navigation on error
      router.push(href);
    }
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center gap-3 text-[#00ff87]">
            <div className="w-8 h-8 border-2 border-[#00ff87] border-t-transparent rounded-full animate-spin" />
            <span className="text-lg font-medium">載入中...</span>
          </div>
        </div>
      );
    }

    switch (currentContent) {
      case "dashboard":
        return <DashboardContent />;
      case "profile":
        return <ProfileContent />;
      case "leaderboard":
        return <LeaderboardContent />;
      case "services":
        return <ServicesContent />;
      // 临时移除的内容组件：
      // case "tasks":
      //   return <TasksContent />;
      // case "support":
      //   return <SupportContent />;
      // case "feedback":
      //   return <FeedbackContent />;
      default:
        return <DashboardContent />;
    }
  };

  return (
    <div className="w-full">
      {renderContent()}
    </div>
  );
};

export default ContentManager;
