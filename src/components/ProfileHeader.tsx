"use client";

import React, { useState, useRef } from "react";
import { motion } from "framer-motion";
import {
  Edit3,
  Camera,
  Check,
  X,
  Upload,
  User,
  Calendar,
  Star,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useProfile, type UserProfile } from "@/hooks/useProfile";
import { AvatarService } from "@/services/avatarService";

// Types
interface ProfileHeaderProps {
  userProfile?: UserProfile | null;
  onProfileUpdate?: (updates: Partial<UserProfile>) => void;
  className?: string;
  editable?: boolean;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  userProfile: externalProfile,
  onProfileUpdate,
  className = "",
  editable = true,
}) => {
  const { user } = useAuth();
  const { profile: internalProfile, updateProfile } = useProfile();

  // Use external profile if provided, otherwise use internal profile
  const userProfile = externalProfile || internalProfile;

  // State management
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const [isEditingMotto, setIsEditingMotto] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [tempUsername, setTempUsername] = useState(
    userProfile?.full_name || ""
  );
  const [tempMotto, setTempMotto] = useState(userProfile?.motto || "");
  const [previewAvatar, setPreviewAvatar] = useState<string | null>(null);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const usernameInputRef = useRef<HTMLInputElement>(null);
  const mottoInputRef = useRef<HTMLInputElement>(null);

  // Helper functions
  const getXpForLevel = (level: number): number => {
    // Simple XP calculation - each level requires 1000 more XP than the previous
    return level * 1000;
  };

  const getCurrentXp = (): number => {
    return userProfile?.experience_points || 0;
  };

  const getXpForNextLevel = (): number => {
    const currentLevel = userProfile?.level || 1;
    return getXpForLevel(currentLevel + 1);
  };

  const getXpProgress = (): number => {
    const currentXp = getCurrentXp();
    const currentLevelXp = getXpForLevel(userProfile?.level || 1);
    const nextLevelXp = getXpForNextLevel();
    const levelXp = currentXp - currentLevelXp;
    const requiredXp = nextLevelXp - currentLevelXp;
    return Math.max(0, Math.min(100, (levelXp / requiredXp) * 100));
  };

  const formatJoinDate = (date: Date | null): string => {
    if (!date) return "未知";
    try {
      return date
        .toLocaleDateString("zh-CN", {
          year: "numeric",
          month: "2-digit",
        })
        .replace("/", "/");
    } catch {
      return "未知";
    }
  };

  const formatUserNumericId = (id: number | null): string => {
    if (!id) return "未分配";
    return `#${id.toString().padStart(8, "0")}`;
  };

  // Username editing handlers
  const handleStartEditUsername = () => {
    setIsEditingUsername(true);
    setTempUsername(userProfile?.full_name || "");
    setTimeout(() => usernameInputRef.current?.focus(), 100);
  };

  const handleSaveUsername = async () => {
    if (tempUsername.trim() && tempUsername !== userProfile?.full_name) {
      try {
        await updateProfile({ full_name: tempUsername.trim() });
        onProfileUpdate?.({
          ...userProfile,
          full_name: tempUsername.trim(),
        } as UserProfile);
      } catch (error) {
        console.error("Error updating username:", error);
      }
    }
    setIsEditingUsername(false);
  };

  const handleCancelEditUsername = () => {
    setTempUsername(userProfile?.full_name || "");
    setIsEditingUsername(false);
  };

  // Motto editing handlers
  const handleStartEditMotto = () => {
    setIsEditingMotto(true);
    setTempMotto(userProfile?.motto || "");
    setTimeout(() => mottoInputRef.current?.focus(), 100);
  };

  const handleSaveMotto = async () => {
    try {
      await updateProfile({ motto: tempMotto });
      onProfileUpdate?.({ ...userProfile, motto: tempMotto } as UserProfile);
    } catch (error) {
      console.error("Error updating motto:", error);
    }
    setIsEditingMotto(false);
  };

  const handleCancelEditMotto = () => {
    setTempMotto(userProfile?.motto || "");
    setIsEditingMotto(false);
  };

  // Avatar upload handlers
  const handleAvatarClick = () => {
    if (editable) {
      fileInputRef.current?.click();
    }
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    setIsUploadingAvatar(true);

    try {
      const avatarService = AvatarService.getInstance();

      // Generate thumbnail preview
      const thumbnail = await avatarService.generateThumbnail(file);
      setPreviewAvatar(thumbnail);

      // Resize image if needed
      const resizedFile = await avatarService.resizeImage(file, 400);

      // Upload the resized image
      const newAvatarUrl = await avatarService.uploadAvatar(
        resizedFile,
        user.id
      );

      // Update profile with new avatar URL
      await updateProfile({ avatar_url: newAvatarUrl });
      onProfileUpdate?.({
        ...userProfile,
        avatar_url: newAvatarUrl,
      } as UserProfile);
    } catch (error) {
      console.error("Error uploading avatar:", error);
      const errorMessage =
        error instanceof Error ? error.message : "头像上传失败，请重试";
      alert(errorMessage);
    } finally {
      setIsUploadingAvatar(false);
      setPreviewAvatar(null);
    }
  };

  // Keyboard handlers
  const handleUsernameKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSaveUsername();
    } else if (e.key === "Escape") {
      handleCancelEditUsername();
    }
  };

  const handleMottoKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSaveMotto();
    } else if (e.key === "Escape") {
      handleCancelEditMotto();
    }
  };

  // Don't render if no profile data
  if (!userProfile) {
    return (
      <div className={`relative ${className}`}>
        <div className="relative bg-gradient-to-br from-[#1a1a2e] via-[#2a2a3e] to-[#1a1a2e] rounded-2xl p-8 shadow-xl overflow-hidden border border-white/10">
          <div className="text-center text-white/60">加载用户资料中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative bg-gradient-to-br from-[#0f1419] via-[#1a202c] to-[#2d3748] rounded-xl p-3 shadow-xl overflow-hidden border border-[#4ecdc4]/20 backdrop-blur-sm"
      >
        {/* Enhanced Background Effects - Hexagonal Honeycomb Pattern */}
        <div
          className="absolute inset-0 opacity-25"
          style={{
            backgroundImage: `
              radial-gradient(circle at 25% 25%, rgba(78,205,196,0.12) 2px, transparent 3px),
              radial-gradient(circle at 75% 25%, rgba(96,239,255,0.10) 2px, transparent 3px),
              radial-gradient(circle at 50% 75%, rgba(78,205,196,0.08) 2px, transparent 3px),
              radial-gradient(circle at 12.5% 50%, rgba(96,239,255,0.06) 1px, transparent 2px),
              radial-gradient(circle at 87.5% 50%, rgba(78,205,196,0.06) 1px, transparent 2px),
              radial-gradient(circle at 37.5% 12.5%, rgba(96,239,255,0.04) 1px, transparent 2px),
              radial-gradient(circle at 62.5% 87.5%, rgba(78,205,196,0.04) 1px, transparent 2px)
            `,
            backgroundSize: '40px 35px',
            backgroundPosition: '0 0, 20px 0, 10px 17.5px, 30px 17.5px, -10px 17.5px, 5px -8.75px, 15px 26.25px'
          }}
        />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(78,205,196,0.15)_0%,transparent_40%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(96,239,255,0.1)_0%,transparent_40%)]" />

        {/* Animated Corner Accents */}
        <div className="absolute top-0 left-0 w-20 h-20 border-t-2 border-l-2 border-[#4ecdc4]/30 rounded-tl-2xl" />
        <div className="absolute top-0 right-0 w-20 h-20 border-t-2 border-r-2 border-[#60efff]/30 rounded-tr-2xl" />
        <div className="absolute bottom-0 left-0 w-20 h-20 border-b-2 border-l-2 border-[#60efff]/30 rounded-bl-2xl" />
        <div className="absolute bottom-0 right-0 w-20 h-20 border-b-2 border-r-2 border-[#4ecdc4]/30 rounded-br-2xl" />

        {/* Profile Section */}
        <div className="relative flex flex-col lg:flex-row justify-between items-start lg:items-center mb-3 gap-2">
          {/* Avatar and Basic Info */}
          <div className="flex items-center gap-3">
            {/* Avatar */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 200, damping: 15 }}
              className="relative group"
            >
              <div
                className={`relative w-20 h-20 rounded-lg border-2 border-[#4ecdc4]/30 overflow-hidden shadow-lg shadow-[#4ecdc4]/20 ${
                  editable ? "cursor-pointer" : ""
                }`}
                onClick={handleAvatarClick}
              >
                <img
                  src={
                    previewAvatar ||
                    userProfile.avatar_url ||
                    "/default-avatar.png"
                  }
                  alt={userProfile.full_name || "用户头像"}
                  className="w-full h-full object-cover transition-all duration-300 group-hover:scale-110"
                />

                {/* Upload Overlay */}
                {editable && (
                  <div className="absolute inset-0 bg-gradient-to-br from-[#4ecdc4]/80 to-[#60efff]/80 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center backdrop-blur-sm">
                    {isUploadingAvatar ? (
                      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white" />
                    ) : (
                      <Camera className="w-8 h-8 text-white drop-shadow-lg" />
                    )}
                  </div>
                )}

                {/* Premium Badge */}
                {userProfile.is_premium_member && (
                  <div className="absolute -top-3 -right-3 w-8 h-8 rounded-full bg-gradient-to-r from-[#FFD700] to-[#FFA500] flex items-center justify-center shadow-lg shadow-[#FFD700]/30 border-2 border-white/20">
                    <Star className="w-4 h-4 text-white" />
                  </div>
                )}
              </div>

              {/* Hidden File Input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                className="hidden"
              />
            </motion.div>

            {/* User Info */}
            <div className="space-y-2 flex-1">
              {/* Username */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="flex items-center gap-3"
              >
                {isEditingUsername ? (
                  <div className="flex items-center gap-2 w-full">
                    <input
                      ref={usernameInputRef}
                      type="text"
                      value={tempUsername}
                      onChange={(e) => setTempUsername(e.target.value)}
                      onKeyDown={handleUsernameKeyDown}
                      className="bg-[#2a2a3e]/50 text-white text-2xl font-bold rounded-xl px-4 py-2 border border-[#4ecdc4]/30 focus:outline-none focus:border-[#4ecdc4] focus:ring-2 focus:ring-[#4ecdc4]/20 backdrop-blur-sm min-w-0 flex-1"
                      maxLength={50}
                    />
                    <button
                      onClick={handleSaveUsername}
                      className="p-2 rounded-xl bg-[#4ecdc4] text-white hover:bg-[#4ecdc4]/80 transition-all shadow-lg shadow-[#4ecdc4]/30"
                    >
                      <Check className="w-4 h-4" />
                    </button>
                    <button
                      onClick={handleCancelEditUsername}
                      className="p-2 rounded-xl bg-white/10 text-white hover:bg-white/20 transition-all"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center gap-3 group w-full">
                    <h2 className="text-2xl font-black text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.5)] bg-gradient-to-r from-white to-[#4ecdc4] bg-clip-text text-transparent">
                      {userProfile.full_name || "未設置用戶名"}
                    </h2>
                    {editable && (
                      <button
                        onClick={handleStartEditUsername}
                        className="opacity-0 group-hover:opacity-100 p-2 rounded-xl bg-white/10 text-white hover:bg-white/20 transition-all"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                )}
              </motion.div>

              {/* Unique ID and Join Date */}
              <div className="flex items-center gap-2 text-[#4ecdc4]/80 text-xs bg-[#2a2a3e]/20 rounded-md px-2 py-1 backdrop-blur-sm w-fit">
                <User className="w-3 h-3" />
                <span className="font-medium">
                  ID: {formatUserNumericId(userProfile.user_numeric_id)}
                </span>
              </div>

              {/* Motto */}
              <div className="max-w-lg">
                {isEditingMotto ? (
                  <div className="flex items-center gap-2">
                    <input
                      ref={mottoInputRef}
                      type="text"
                      value={tempMotto}
                      onChange={(e) => setTempMotto(e.target.value)}
                      onKeyDown={handleMottoKeyDown}
                      placeholder="添加個人簽名..."
                      className="bg-[#2a2a3e]/50 text-white rounded-xl px-4 py-2 border border-[#4ecdc4]/30 focus:outline-none focus:border-[#4ecdc4] focus:ring-2 focus:ring-[#4ecdc4]/20 backdrop-blur-sm flex-1"
                      maxLength={100}
                    />
                    <button
                      onClick={handleSaveMotto}
                      className="p-2 rounded-xl bg-[#4ecdc4] text-white hover:bg-[#4ecdc4]/80 transition-all shadow-lg shadow-[#4ecdc4]/30"
                    >
                      <Check className="w-4 h-4" />
                    </button>
                    <button
                      onClick={handleCancelEditMotto}
                      className="p-2 rounded-xl bg-white/10 text-white hover:bg-white/20 transition-all"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 group">
                    <span className="text-white/90 italic text-sm bg-[#2a2a3e]/20 rounded-md px-3 py-1 backdrop-blur-sm border border-white/10">
                      {userProfile.motto ||
                        (editable ? "點擊添加個人簽名..." : "暫無個人簽名")}
                    </span>
                    {editable && (
                      <button
                        onClick={handleStartEditMotto}
                        className="opacity-0 group-hover:opacity-100 p-2 rounded-xl bg-white/10 text-white hover:bg-white/20 transition-all"
                      >
                        <Edit3 className="w-3 h-3" />
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Level Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-br from-[#4ecdc4] via-[#60efff] to-[#4ecdc4] rounded-xl p-3 text-center shadow-xl shadow-[#4ecdc4]/30 border border-white/20 backdrop-blur-sm relative overflow-hidden"
          >
            {/* Level Badge Background Effect */}
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.1)_0%,transparent_70%)]" />
            <div className="relative">
              <div className="text-white/90 text-xs font-medium mb-0.5">等級</div>
              <div className="text-xl font-black text-white mb-0.5 drop-shadow-lg">
                {userProfile.level || 1}
              </div>
              <div className="text-white/70 text-[10px] uppercase tracking-wider">Level</div>
            </div>
          </motion.div>
        </div>

        {/* Compact XP Progress */}
        <div className="relative mb-3">
          <div className="flex justify-between items-center mb-1.5">
            <div className="flex items-center gap-1.5">
              <div className="w-6 h-6 rounded-md bg-gradient-to-r from-[#4ecdc4]/20 to-[#60efff]/20 flex items-center justify-center border border-[#4ecdc4]/30">
                <Star className="w-3 h-3 text-[#4ecdc4]" />
              </div>
              <span className="text-white font-bold text-xs">
                Level {userProfile.level || 1} 經驗進度
              </span>
            </div>
            <span className="text-white/80 text-xs">
              {getCurrentXp().toLocaleString()} / {getXpForNextLevel().toLocaleString()} XP
            </span>
          </div>
          <div className="relative">
            <div className="h-3 bg-[#2a2a3e]/50 rounded-full overflow-hidden border border-[#4ecdc4]/20">
              <motion.div
                initial={{ width: 0 }}
                animate={{
                  width: `${getXpProgress()}%`,
                }}
                transition={{ duration: 1.5, ease: "easeOut" }}
                className="h-full bg-gradient-to-r from-[#4ecdc4] via-[#60efff] to-[#4ecdc4] rounded-full relative"
              >
                <motion.div
                  animate={{ x: ["-100%", "100%"] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full"
                />
              </motion.div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-white text-[10px] font-medium drop-shadow-lg">
                {Math.round(getXpProgress())}%
              </span>
            </div>
          </div>
        </div>

        {/* Compact Creator Statistics */}
        <div className="grid grid-cols-3 gap-2">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-br from-[#2a2a3e]/40 to-[#1a1a2e]/40 rounded-md p-2 text-center border border-[#f39c12]/20 hover:border-[#f39c12]/40 transition-all duration-300 backdrop-blur-sm group hover:shadow-md hover:shadow-[#f39c12]/20"
          >
            <div className="text-[#f39c12] text-lg mb-0.5 group-hover:scale-110 transition-transform duration-300">⚡</div>
            <div className="text-[10px] text-white/60 mb-0.5 font-medium">總經驗</div>
            <div className="text-xs font-black text-white">{userProfile.experience_points?.toLocaleString() || 0}</div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-gradient-to-br from-[#2a2a3e]/40 to-[#1a1a2e]/40 rounded-md p-2 text-center border border-[#4ecdc4]/20 hover:border-[#4ecdc4]/40 transition-all duration-300 backdrop-blur-sm group hover:shadow-md hover:shadow-[#4ecdc4]/20"
          >
            <div className="text-[#4ecdc4] text-lg mb-0.5 group-hover:scale-110 transition-transform duration-300">🏆</div>
            <div className="text-[10px] text-white/60 mb-0.5 font-medium">完成度</div>
            <div className="text-xs font-black text-white">{userProfile.profile_completion_percentage || 0}%</div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-gradient-to-br from-[#2a2a3e]/40 to-[#1a1a2e]/40 rounded-md p-2 text-center border border-[#60efff]/20 hover:border-[#60efff]/40 transition-all duration-300 backdrop-blur-sm group hover:shadow-md hover:shadow-[#60efff]/20"
          >
            <div className="text-[#60efff] text-lg mb-0.5 group-hover:scale-110 transition-transform duration-300">🎯</div>
            <div className="text-[10px] text-white/60 mb-0.5 font-medium">目標</div>
            <div className="text-xs font-black text-white">升級</div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default ProfileHeader;
