"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Youtube, Plus, BarChart3, Star } from "lucide-react";

interface Video {
  id: string;
  thumbnail: string;
  title: string;
  views: number;
  likes: number;
  duration: string;
  publishedAt: string;
  status: string;
  url: string;
  description?: string;
}

interface ChannelSectionProps {
  // Can accept either channelId, username, or handle
  channelId?: string;
  username?: string;
  handle?: string;
  isVisible?: boolean;
}

const ChannelSection: React.FC<ChannelSectionProps> = ({
  channelId,
  username,
  handle,
  isVisible = true,
}) => {
  const [videos, setVideos] = useState<Video[]>([]);
  const [channelData, setChannelData] = useState<{
    channelId: string;
    title: string;
    subscribers: number | string;
    views: number;
    videos: number;
    thumbnailUrl: string;
    uploadsPlaylistId: string;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch channel data and videos using public API
  const fetchYouTubeData = async () => {
    if (!channelId && !username && !handle) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Build query parameters for public channel API
      const params = new URLSearchParams();
      if (channelId) params.append("channelId", channelId);
      if (username) params.append("username", username);
      if (handle) params.append("handle", handle);

      // Fetch public channel data
      const channelResponse = await fetch(
        `/api/youtube/public-channel?${params}`
      );

      if (!channelResponse.ok) {
        const errorData = await channelResponse.json();
        throw new Error(errorData.error || "Failed to fetch channel data");
      }

      const channelInfo = await channelResponse.json();
      setChannelData(channelInfo);

      if (!channelInfo.uploadsPlaylistId) {
        throw new Error("No uploads playlist found");
      }

      // Fetch videos from the uploads playlist (public API)
      const videosResponse = await fetch(
        `/api/youtube/videos?playlistId=${channelInfo.uploadsPlaylistId}&maxResults=10`
      );

      if (!videosResponse.ok) {
        const errorData = await videosResponse.json();
        throw new Error(errorData.error || "Failed to fetch videos");
      }

      const videosData = await videosResponse.json();
      setVideos(videosData.videos || []);
    } catch (err) {
      console.error("Error fetching YouTube data:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch data");
    } finally {
      setLoading(false);
    }
  };

  // Format numbers for display
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  // Fetch data when component mounts or when props change
  useEffect(() => {
    if (channelId || username || handle) {
      fetchYouTubeData();
    }
  }, [channelId, username, handle]);

  if (!isVisible) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.6 }}
      className="bg-white/5 backdrop-blur-sm rounded-lg p-3 border border-white/10"
    >
      {/* Header Section - Always Show */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#ff0000] to-[#cc0000] flex items-center justify-center">
            <Youtube className="w-4 h-4 text-white" />
          </div>
          <div>
            <h3 className="text-base font-bold text-white">
              YouTube 頻道
            </h3>
            <p className="text-xs text-white/60">
              {loading ? "載入中..." :
               error ? "載入失敗" :
               channelData ? `${channelData.title} · ${videos.length} 影片` :
               "尚未連接"}
            </p>
          </div>
          {loading && (
            <div className="w-4 h-4 border-2 border-[#ff0000] border-t-transparent rounded-full animate-spin" />
          )}
        </div>
        {channelData && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-2 py-1 bg-gradient-to-r from-[#ff0000] to-[#cc0000] text-white rounded-md text-xs font-medium shadow-lg shadow-[#ff0000]/20 flex items-center gap-1"
            onClick={() => {
              window.open(
                `https://studio.youtube.com/channel/${channelData.channelId}`,
                "_blank"
              );
            }}
          >
            <Plus className="w-3 h-3" />
            查看頻道
          </motion.button>
        )}
      </div>

      {/* Content Section */}
      {error && (
        <div className="mb-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 rounded-lg bg-red-500/20 flex items-center justify-center flex-shrink-0">
              <Youtube className="w-4 h-4 text-red-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-red-400 font-medium text-sm mb-1">連接失敗</h4>
              <p className="text-red-300/80 text-xs mb-2">無法載入 YouTube 頻道數據</p>
              <button
                onClick={fetchYouTubeData}
                className="text-red-300 hover:text-red-200 underline text-xs"
              >
                重新嘗試
              </button>
            </div>
          </div>
        </div>
      )}

      {!channelData && !loading && !error && (
        <div className="p-4 bg-white/5 rounded-lg border border-white/10">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center flex-shrink-0">
              <Youtube className="w-4 h-4 text-white/50" />
            </div>
            <div className="flex-1">
              <h4 className="text-white/80 font-medium text-sm mb-1">尚未連接 YouTube</h4>
              <p className="text-white/60 text-xs">連接您的 YouTube 頻道以展示影片內容</p>
            </div>
          </div>
        </div>
      )}

      {channelData && videos.length === 0 && !loading && !error && (
        <div className="p-4 bg-white/5 rounded-lg border border-white/10">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 rounded-lg bg-[#ff0000]/20 flex items-center justify-center flex-shrink-0">
              <Youtube className="w-4 h-4 text-[#ff0000]" />
            </div>
            <div className="flex-1">
              <h4 className="text-white/80 font-medium text-sm mb-1">頻道已連接</h4>
              <p className="text-white/60 text-xs">暫無影片內容，開始上傳您的第一個影片吧！</p>
            </div>
          </div>
        </div>
      )}

      {videos.length > 0 && (
        <>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
            {videos.slice(0, 6).map((video, index) => (
              <motion.div
                key={video.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className="group relative bg-white/5 rounded-md overflow-hidden hover:bg-white/10 transition-all border border-white/10 cursor-pointer"
                onClick={() => window.open(video.url, "_blank")}
              >
                <div className="aspect-video bg-black/20 relative">
                  {video.thumbnail ? (
                    <img
                      src={video.thumbnail}
                      alt={video.title}
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Youtube className="w-8 h-8 text-white/30" />
                    </div>
                  )}
                  <div className="absolute bottom-1 right-1 px-1.5 py-0.5 bg-black/70 text-white text-[10px] rounded">
                    {video.duration}
                  </div>
                  {video.status === "审核中" && (
                    <div className="absolute top-1 left-1 px-1.5 py-0.5 bg-yellow-500/90 text-white text-[10px] rounded-sm">
                      审核中
                    </div>
                  )}
                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                        <div className="w-0 h-0 border-l-[6px] border-l-white border-y-[4px] border-y-transparent ml-0.5" />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="p-2">
                  <h4 className="text-xs text-white font-medium line-clamp-2 mb-1 leading-tight">
                    {video.title}
                  </h4>
                  <div className="flex items-center justify-between text-[10px] text-white/60">
                    <div className="flex items-center gap-1">
                      <BarChart3 className="w-3 h-3" />
                      {formatNumber(video.views)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="w-3 h-3" />
                      {formatNumber(video.likes)}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
          
          {/* 提示用户连接自己的账号 */}
          {(!channelId || channelId === "UCX6OQ3DkcsbYNE6H8uQQuVA" || handle === "@MrBeast") && (
            <div className="mt-3 p-3 bg-gradient-to-r from-[#4ecdc4]/10 to-[#60efff]/10 border border-[#4ecdc4]/20 rounded-lg">
              <div className="flex items-center gap-2 text-sm">
                <div className="w-5 h-5 rounded-full bg-[#4ecdc4]/20 flex items-center justify-center flex-shrink-0">
                  <span className="text-[#4ecdc4] text-xs">💡</span>
                </div>
                <p className="text-[#4ecdc4] font-medium">
                  這是示範頻道，前往 
                  <span className="font-bold mx-1">服務管理</span>
                  連接您自己的 YouTube 帳號
                </p>
              </div>
            </div>
          )}
        </>
      )}
    </motion.div>
  );
};

export default ChannelSection;
