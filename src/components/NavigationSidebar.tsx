"use client";

import React from "react";
import { Card } from "@/components/ui/card";
import {
  Home,
  User,
  Trophy,
  Settings,
  ChevronRight,
  LogOut,
  Share2,
  // 临时移除的图标：
  // HelpCircle,
  // MessageSquare,
  // Zap,
} from "lucide-react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { useProfile } from "@/hooks/useProfile";

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href: string;
  description?: string;
}

interface NavigationSidebarProps {
  className?: string;
  onNavigate?: (item: NavigationItem) => void;
}

const NavigationSidebar: React.FC<NavigationSidebarProps> = ({
  className = "",
  onNavigate,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { user, signOut } = useAuth();
  const { profile } = useProfile();

  const handleLogout = async () => {
    try {
      await signOut();
      router.push("/");
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const getUserDisplayName = () => {
    if (user?.name) return user.name;
    if (profile?.full_name) return profile.full_name;
    return "Creator";
  };

  const getUserEmail = () => {
    return user?.email || "用户邮箱";
  };

  const userMenuItems: NavigationItem[] = [
    {
      id: "share",
      label: "分享邀請",
      icon: <Share2 className="w-5 h-5" />,
      href: "#",
      description: "邀請碼分享",
    },
    {
      id: "logout",
      label: "退出登錄",
      icon: <LogOut className="w-5 h-5" />,
      href: "#",
      description: "安全退出",
    },
  ];

  const navigationItems: NavigationItem[] = [
    {
      id: "dashboard",
      label: "創作者中心",
      icon: <Home className="w-5 h-5" />,
      href: "/dashboard",
      description: "個人資訊與YouTube頻道",
    },
    {
      id: "leaderboard",
      label: "創作者榜單",
      icon: <Trophy className="w-5 h-5" />,
      href: "/leaderboard",
      description: "查看排行榜",
    },
    {
      id: "services",
      label: "服務管理",
      icon: <Settings className="w-5 h-5" />,
      href: "/services",
      description: "帳號綁定與服務設定",
    },
    // 临时移除的功能模块：
    // {
    //   id: "tasks",
    //   label: "任務中心",
    //   icon: <Zap className="w-5 h-5" />,
    //   href: "/tasks",
    //   description: "管理任務與獎勵",
    // },
    // {
    //   id: "support",
    //   label: "幫助支援",
    //   icon: <HelpCircle className="w-5 h-5" />,
    //   href: "/support",
    //   description: "獲取幫助與支援",
    // },
    // {
    //   id: "feedback",
    //   label: "意見回饋",
    //   icon: <MessageSquare className="w-5 h-5" />,
    //   href: "/feedback",
    //   description: "提供意見與建議",
    // },
  ];

  const handleItemClick = (item: NavigationItem) => {
    // Handle special user menu actions
    if (item.id === "logout") {
      handleLogout();
      return;
    }
    
    if (item.id === "share") {
      // Handle share functionality here
      console.log("Share functionality clicked");
      return;
    }

    if (onNavigate) {
      onNavigate(item);
    } else {
      router.push(item.href);
    }
  };

  const isActive = (href: string) => {
    return pathname === href;
  };

  return (
    <aside className={`w-64 flex-shrink-0 ${className}`}>
      <div className="space-y-2 lg:space-y-2 py-4">
        {/* Navigation Header */}
        <Card className="relative bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl px-4 py-3 shadow-lg overflow-hidden">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-[#00ff87] to-[#60efff] flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-sm">VG</span>
            </div>
            <div>
              <h2 className="text-white font-bold text-lg">導航選單</h2>
              <p className="text-gray-400 text-xs">快速存取功能</p>
            </div>
          </div>
        </Card>

        {/* Navigation Items */}
        <div className="space-y-1">
          {navigationItems.map((item) => (
            <button
              key={item.id}
              onClick={() => handleItemClick(item)}
              className={`
                relative w-full text-left p-3 rounded-xl transition-all duration-300 group
                ${
                  isActive(item.href)
                    ? "bg-[#00ff87]/20 border border-[#00ff87]/40 text-[#00ff87]"
                    : "bg-[#1a1a2e]/60 border border-[#2a2a3e] text-gray-300 hover:bg-[#2a2a3e]/80 hover:border-[#00ff87]/20 hover:text-[#00ff87]"
                }
              `}
            >
              {/* Active indicator */}
              {isActive(item.href) && (
                <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-[#00ff87] to-[#60efff] rounded-r-full" />
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div
                    className={`
                    p-2 rounded-lg transition-all duration-300
                    ${
                      isActive(item.href)
                        ? "bg-[#00ff87]/20 text-[#00ff87]"
                        : "bg-[#2a2a3e]/50 text-gray-400 group-hover:bg-[#00ff87]/10 group-hover:text-[#00ff87]"
                    }
                  `}
                  >
                    {item.icon}
                  </div>
                  <div>
                    <div
                      className={`
                      font-medium transition-colors duration-300
                      ${
                        isActive(item.href)
                          ? "text-[#00ff87]"
                          : "text-white group-hover:text-[#00ff87]"
                      }
                    `}
                    >
                      {item.label}
                    </div>
                    {item.description && (
                      <div className="text-xs text-gray-500 group-hover:text-gray-400 transition-colors duration-300">
                        {item.description}
                      </div>
                    )}
                  </div>
                </div>
                <ChevronRight
                  className={`
                    w-4 h-4 transition-all duration-300
                    ${
                      isActive(item.href)
                        ? "text-[#00ff87] translate-x-1"
                        : "text-gray-500 group-hover:text-[#00ff87] group-hover:translate-x-1"
                    }
                  `}
                />
              </div>
            </button>
          ))}
        </div>

        {/* User Info Card */}
        <Card className="relative bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl px-4 py-3 shadow-lg overflow-hidden group hover:bg-[#1a1a2e]/90 transition-all duration-300">
          {/* Corner decorations */}
          <div className="absolute top-0 left-0 w-3 h-3 border-t-2 border-l-2 border-[#2a2a3e] rounded-tl-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
          <div className="absolute top-0 right-0 w-3 h-3 border-t-2 border-r-2 border-[#2a2a3e] rounded-tr-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
          <div className="absolute bottom-0 left-0 w-3 h-3 border-b-2 border-l-2 border-[#2a2a3e] rounded-bl-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
          <div className="absolute bottom-0 right-0 w-3 h-3 border-b-2 border-r-2 border-[#2a2a3e] rounded-br-xl group-hover:border-[#00ff87]/30 transition-colors duration-300" />
          
          <div className="space-y-3">
            {/* User Info */}
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-[#00ff87] to-[#60efff] flex items-center justify-center text-white font-bold text-sm shadow-lg">
                {getUserDisplayName().charAt(0).toUpperCase()}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-white font-medium text-sm truncate">
                  {getUserDisplayName()}
                </div>
                <div className="text-[#00ff87]/70 text-xs truncate">
                  {getUserEmail()}
                </div>
              </div>
            </div>
            
            {/* User Menu Items */}
            <div className="space-y-1">
              {userMenuItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleItemClick(item)}
                  className={`
                    relative w-full text-left p-2 rounded-lg transition-all duration-300 group/item
                    ${
                      item.id === "logout"
                        ? "text-red-400 hover:bg-red-500/10 hover:text-red-300"
                        : item.id === "share"
                        ? "text-[#00ff87] hover:bg-[#00ff87]/10"
                        : "text-gray-300 hover:bg-[#2a2a3e]/60 hover:text-[#00ff87]"
                    }
                  `}
                >
                  <div className="flex items-center gap-2">
                    <div className="flex-shrink-0">
                      {item.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium truncate">{item.label}</div>
                      {item.description && (
                        <div className="text-xs opacity-70 truncate">{item.description}</div>
                      )}
                    </div>
                    {item.id === "share" && (
                      <div className="flex-shrink-0">
                        <span className="w-2 h-2 bg-[#00ff87] rounded-full animate-pulse" />
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </Card>

        {/* Footer Info */}
        <Card className="relative bg-[#1a1a2e]/60 backdrop-blur-sm border border-[#2a2a3e] rounded-xl px-4 py-3 shadow-lg">
          <div className="text-center">
            <div className="text-xs text-gray-500 mb-1">VG蜂巢創作者平台</div>
            <div className="text-xs text-gray-600">v1.0.0</div>
          </div>
        </Card>
      </div>
    </aside>
  );
};

export default NavigationSidebar;
