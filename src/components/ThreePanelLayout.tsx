"use client";

import React, { useState, useEffect } from "react";
import { useLayout } from "@/contexts/LayoutContext";
import NavigationSidebar from "@/components/NavigationSidebar";
import BountySidebar from "@/components/BountySidebar";
import DashboardHeader from "@/components/DashboardHeader";
import ContentManager from "@/components/ContentManager";
import { useRouter, usePathname } from "next/navigation";

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href: string;
  description?: string;
}

interface ThreePanelLayoutProps {
  children?: React.ReactNode;
  showBountySidebar?: boolean;
  className?: string;
  enableDynamicContent?: boolean;
  forceContent?: string; // New prop to force specific content
}

const ThreePanelLayout: React.FC<ThreePanelLayoutProps> = ({
  children,
  showBountySidebar,
  className = "",
  enableDynamicContent = false,
  forceContent,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { layoutState, setNavigating, toggleMobileNav } = useLayout();
  const [currentContent, setCurrentContent] = useState<React.ReactNode>(children);

  // Use layout context state or prop override
  const shouldShowBountySidebar = showBountySidebar !== undefined ? showBountySidebar : layoutState.showHomeSidebar;
  const isNavigating = layoutState.isNavigating;
  const showMobileNav = layoutState.showMobileNav;

  // Update content when children prop changes or when forceContent is provided
  useEffect(() => {
    if (!enableDynamicContent && !forceContent) {
      setCurrentContent(children);
    }
  }, [children, enableDynamicContent, forceContent]);

  const handleNavigation = async (item: NavigationItem) => {
    // Enhanced navigation logic to support all dashboard-related routes
    const dashboardRoutes = ['/dashboard', '/profile', '/leaderboard', '/services'];
    // 临时移除的路由: '/tasks', '/support', '/feedback'
    const isDashboardRoute = dashboardRoutes.some(route => item.href.startsWith(route)) || item.id === 'dashboard';

    if (isDashboardRoute && enableDynamicContent) {
      setNavigating(true);

      // Add a small delay for smooth transition
      setTimeout(() => {
        setNavigating(false);
      }, 300);

      // Update URL without full page reload for dashboard-related routes
      window.history.pushState({}, '', item.href);

      // Trigger content update in ContentManager
      window.dispatchEvent(new CustomEvent('navigate', { detail: { href: item.href, id: item.id } }));
    } else {
      // For other routes, use normal navigation
      router.push(item.href);
    }
  };

  return (
    <div className={`min-h-screen ${className}`}>
      {/* Header */}
      <DashboardHeader />

      {/* Main Content Area - Fixed Height Layout */}
      <main className="fixed top-[65px] left-0 right-0 bottom-0 lg:bottom-0 flex">
        <div className="flex w-full max-w-[1800px] mx-auto px-4 gap-4 lg:gap-6">
          {/* Left Panel - Navigation Sidebar (Fixed) */}
          <div className="hidden lg:block flex-shrink-0 sticky top-0 h-full overflow-y-auto custom-scrollbar">
            <NavigationSidebar
              onNavigate={handleNavigation}
              className=""
            />
          </div>

          {/* Middle Panel - Main Content (Scrollable) */}
          <section
            className={`
              flex-1 min-w-0 transition-all duration-300 overflow-y-auto h-full py-4 pb-20 lg:pb-4 scrollbar-minimal
              ${isNavigating ? 'opacity-50' : 'opacity-100'}
              ${!shouldShowBountySidebar ? 'lg:mr-0' : ''}
            `}
          >
            {/* Loading overlay for navigation transitions */}
            {isNavigating && (
              <div className="absolute inset-0 bg-[#0f1419]/50 backdrop-blur-sm z-10 flex items-center justify-center rounded-xl">
                <div className="flex items-center gap-3 text-[#00ff87]">
                  <div className="w-6 h-6 border-2 border-[#00ff87] border-t-transparent rounded-full animate-spin" />
                  <span className="text-sm font-medium">載入中...</span>
                </div>
              </div>
            )}

            {/* Content Area */}
            <div className="relative">
              {enableDynamicContent ? (
                <ContentManager
                  currentPath={pathname || "/"}
                  onNavigate={handleNavigation}
                />
              ) : (
                currentContent
              )}
            </div>
          </section>

          {/* Right Panel - Bounty Sidebar (Fixed) */}
          {shouldShowBountySidebar && (
            <div className="hidden md:block w-full md:w-[340px] flex-shrink-0 sticky top-0 h-full overflow-y-auto custom-scrollbar">
              <BountySidebar className="" />
            </div>
          )}
        </div>
      </main>

      {/* Mobile Navigation Overlay */}
      <div className="lg:hidden fixed bottom-4 left-4 right-4 z-50">
        <div className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-3 shadow-lg">
          <div className="flex items-center justify-between">
            <span className="text-white text-sm font-medium">快速導航</span>
            <button
              onClick={() => toggleMobileNav()}
              className="text-[#00ff87] hover:text-[#60efff] transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Modal */}
      {showMobileNav && (
        <>
          <div
            className="lg:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            onClick={() => toggleMobileNav()}
          />
          <div className="lg:hidden fixed bottom-20 left-4 right-4 z-50">
            <div className="bg-[#1a1a2e]/95 backdrop-blur-sm border border-[#2a2a3e] rounded-xl p-4 shadow-lg max-h-[60vh] overflow-y-auto custom-scrollbar">
              <NavigationSidebar
                onNavigate={(item) => {
                  handleNavigation(item);
                  toggleMobileNav();
                }}
                className="w-full"
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ThreePanelLayout;
