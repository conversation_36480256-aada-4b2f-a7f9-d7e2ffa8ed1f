"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useUser } from "@clerk/nextjs";
import { useProfile, type UserProfile } from "@/hooks/useProfile";
import {
  Trophy,
  Wallet,
  BarChart3,
  Star,
  ChevronRight,
  Link,
  Unlink,
  Plus,
  Sparkles,
  Youtube,
  Twitter,
  MessageSquare,
  Mail,
  Send,
} from "lucide-react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import Particles from "react-tsparticles";
import { loadSlim } from "tsparticles-slim";
import type { Engine } from "tsparticles-engine";
import AchievementsSection from "@/components/AchievementsSection";

import AccountConnections from "@/components/AccountConnections";
import ProfileHeader from "@/components/ProfileHeader";
import DashboardHeader from "@/components/DashboardHeader";
import {
  useConnectionActions,
  useConnections,
  useConnectionStatus,
  type ConnectionsData,
} from "@/hooks/usePlatformConnections";

import ChannelSection from "@/components/ChannelSection";

// Import separated modules
import {
  mockUserData,
  mockVideos,
  mockAchievements,
  particlesConfig,
  YOUTUBE_CLIENT_ID,
  YOUTUBE_REDIRECT_URI,
  YOUTUBE_SCOPE,
  authUrl,
} from "./mockData";
import {
  getRarityColor,
  getStatusColor,
  GameButton,
  StatCard,
} from "./components";
import YouTubeStatsCard from "./YouTubeStatsCard";
import CollaborationSection from "./CollaborationSection";

import ServicesDisplay from "@/components/services/ServicesDisplay";

const Profile: React.FC = () => {
  const { user, isLoading } = useAuth();
  const { user: clerkUser } = useUser();
  const { profile, isLoading: profileLoading, updateProfile } = useProfile();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [showOAuthPrompt, setShowOAuthPrompt] = useState(false);
  const [showTransactionHistory, setShowTransactionHistory] = useState(false);
  // 移除服务管理标签页，所有服务管理功能都通过弹窗处理

  // Use the correct hooks
  const connections = useConnections();
  const { initializeConnections, syncWithProfile, refreshYouTubeData } =
    useConnectionActions();

  const router = useRouter();

  // Mock states

  // Get YouTube connection data from platform connections
  const youtubeConnection = connections.YouTube;

  console.log("youtube connection", youtubeConnection);

  // Get YouTube data from connection metadata
  const youtubeData = youtubeConnection?.metadata
    ? {
        channelId: youtubeConnection.metadata.channelId as string,
        title: youtubeConnection.metadata.title as string,
        subscribers: youtubeConnection.metadata.subscribers as number,
        views: youtubeConnection.metadata.views as number,
        videos: youtubeConnection.metadata.videos as number,
        thumbnailUrl: youtubeConnection.metadata.thumbnailUrl as string,
      }
    : null;

  const particlesInit = async (engine: Engine) => {
    await loadSlim(engine);
  };

  const handleEditProfile = () => {
    setIsEditingProfile(true);
    // TODO: Implement profile editing logic
  };

  const handleSaveProfile = () => {
    setIsEditingProfile(false);
    // TODO: Implement profile saving logic
  };

  // OAuth callback is now handled by the dedicated /oauth/callback page

  // Initialize platform connections
  useEffect(() => {
    if (user?.id) {
      initializeConnections(user.id);
    }
  }, [user?.id, initializeConnections]);

  // Sync connections store with profile data
  useEffect(() => {
    if (profile) {
      // Sync with profile data - cast safely to unknown first
      syncWithProfile(profile as unknown as Record<string, unknown>);
    }
  }, [profile, syncWithProfile]);

  // Refresh YouTube data when user logs in and has YouTube connection
  useEffect(() => {
    const refreshOnLogin = async () => {
      if (
        user?.id &&
        youtubeConnection?.status === "已绑定" &&
        youtubeConnection.metadata?.channelId
      ) {
        try {
          console.log("Refreshing YouTube data on login...");
          await refreshYouTubeData(user.id);
        } catch (error) {
          console.error("Failed to refresh YouTube data on login:", error);
        }
      }
    };

    refreshOnLogin();
  }, [
    user?.id,
    youtubeConnection?.status,
    youtubeConnection?.metadata?.channelId,
    refreshYouTubeData,
  ]);

  // Handle URL parameters for OAuth success
  useEffect(() => {
    const handleOAuthSuccess = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const success = urlParams.get("success");
      const channel = urlParams.get("channel");

      if (success === "youtube-verified" && user?.id) {
        console.log(
          `YouTube channel "${channel}" verified, refreshing data...`
        );
        try {
          // Wait a moment for the database to be updated
          setTimeout(async () => {
            await refreshYouTubeData(user.id);
          }, 1000);
        } catch (error) {
          console.error(
            "Failed to refresh YouTube data after OAuth success:",
            error
          );
        }

        // Clean up URL parameters
        window.history.replaceState(
          {},
          document.title,
          window.location.pathname
        );
      }
    };

    handleOAuthSuccess();
  }, [user?.id, refreshYouTubeData]);

  // Note: YouTube data refresh is now handled by the platform connections system
  // Data is automatically refreshed when connections are updated

  // Update userProfile when profile data is loaded
  useEffect(() => {
    if (profile) {
      setUserProfile(profile);
    } else if (!profileLoading && !profile) {
      // If no profile exists, create a mock one with the correct structure
      setUserProfile({
        clerk_user_id: user?.id || "",
        email: clerkUser?.emailAddresses?.[0]?.emailAddress || "",
        clerk_username: clerkUser?.username || null,
        full_name: mockUserData.username,
        avatar_url: mockUserData.avatar,
        role: "creator",
        experience_points: mockUserData.currentXp,
        level: mockUserData.level,
        wallet_address: null,
        created_at: new Date(),
        updated_at: new Date(),
        is_premium_member: mockUserData.isPremium,
        youtube_id: null,
        youtube_title: null,
        subscribers: 0,
        views: 0,
        motto: mockUserData.motto,
        user_numeric_id: null,
        profile_completion_percentage: 0,
        last_login_at: new Date(),
        preferred_language: "zh-CN",
        email_verified:
          clerkUser?.emailAddresses?.[0]?.verification?.status === "verified" ||
          false,
      });
    }
  }, [profile, profileLoading, user, clerkUser]);

  // Handler for connection updates from AccountConnections component
  const handleConnectionUpdate = useCallback(
    async (connectionsData: ConnectionsData) => {
      console.log("Connections updated:", connectionsData);

      // YouTube data is now managed by the platform connections system
      // No additional fetch needed here
    },
    []
  );

  const handleViewConnection = (platform: string) => {
    // TODO: Implement view connection logic
  };

  const handleRecharge = () => {
    // TODO: Implement recharge logic
  };

  const handleWithdraw = () => {
    // TODO: Implement withdraw logic
  };

  const handleViewIncomeDetails = () => {
    // TODO: Implement income details view logic
  };

  const handleViewAllActivity = () => {
    // TODO: Implement view all activity logic
  };

  const handleViewTransactionHistory = (
    type: "recharge" | "withdraw" | "reward"
  ) => {
    setShowTransactionHistory(true);
    // TODO: Implement transaction history view logic
  };

  const handleCloseTransactionHistory = () => {
    setShowTransactionHistory(false);
  };

  const handleProfileUpdate = (updates: Partial<UserProfile>) => {
    if (userProfile) {
      setUserProfile({ ...userProfile, ...updates });
      // Sync with backend using the updateProfile function
      if (updates && Object.keys(updates).length > 0) {
        updateProfile(updates);
      }
    }
  };

  const handleCollaborationUpdate = (newCollaboration: any) => {
    mockUserData.collaboration = newCollaboration;
    // TODO: Sync with backend
  };

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-[#0f1419] via-[#1a202c] to-[#2d3748] bg-cover bg-no-repeat relative overflow-x-hidden">
      {/* Particle Background */}
      <Particles
        id="tsparticles"
        init={particlesInit}
        options={particlesConfig}
        className="absolute inset-0 z-0"
      />

      {/* Animated Background Effects - Updated colors with Hexagonal Pattern */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,_rgba(78,205,196,0.15)_0%,_transparent_50%)] opacity-50 animate-pulse" />
      <div
        className="absolute inset-0 opacity-15"
        style={{
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(78,205,196,0.08) 1.5px, transparent 2px),
            radial-gradient(circle at 75% 25%, rgba(96,239,255,0.06) 1.5px, transparent 2px),
            radial-gradient(circle at 50% 75%, rgba(78,205,196,0.04) 1.5px, transparent 2px)
          `,
          backgroundSize: '50px 43px',
          backgroundPosition: '0 0, 25px 0, 12.5px 21.5px'
        }}
      />

      {/* Dashboard Header */}
      <DashboardHeader variant="dashboard" />

      {/* Back Button - Fixed Position */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => router.push("/dashboard")}
        className="fixed top-20 left-4 z-50 px-3 py-2 bg-[#1a202c]/80 backdrop-blur-sm border border-[#4ecdc4]/30 rounded-lg text-white text-sm font-medium hover:bg-[#2d3748]/80 transition-all shadow-lg"
      >
        <div className="flex items-center gap-1">
          <ChevronRight className="w-3 h-3 rotate-180" />
          返回
        </div>
      </motion.button>

      {/* Main Content */}
      <div className="relative container mx-auto px-4 pt-20 pb-8 z-10">
        {/* Character Card */}
        {userProfile && (
          <ProfileHeader
            userProfile={userProfile}
            onProfileUpdate={handleProfileUpdate}
            className="max-w-4xl mx-auto mb-6"
            editable={true}
          />
        )}

        {/* Additional Content Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="relative max-w-4xl mx-auto mb-6"
        >
          <div className="relative bg-gradient-to-br from-[#1a202c] via-[#2d3748] to-[#1a202c] rounded-2xl p-8 shadow-xl overflow-hidden border border-[#4ecdc4]/20">
            {/* Card Background Pattern - Hexagonal */}
            <div
              className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `
                  radial-gradient(circle at 30% 30%, rgba(255,255,255,0.08) 1px, transparent 2px),
                  radial-gradient(circle at 70% 30%, rgba(78,205,196,0.06) 1px, transparent 2px),
                  radial-gradient(circle at 50% 70%, rgba(255,255,255,0.04) 1px, transparent 2px)
                `,
                backgroundSize: '40px 35px',
                backgroundPosition: '0 0, 20px 0, 10px 17.5px'
              }}
            />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(78,205,196,0.1)_0%,transparent_50%)] opacity-30" />

            {/* Stats Grid */}
            <div className="grid grid-cols-3 gap-6 mb-8">
              {/* YouTube Stats */}
              <YouTubeStatsCard youtubeData={youtubeData} delay={0.1} />

              {/* Assets */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3 }}
                className="relative bg-[#4ecdc4]/10 rounded-xl p-4 backdrop-blur-sm group hover:bg-[#4ecdc4]/15 transition-all border border-[#4ecdc4]/20 shadow-lg shadow-[#4ecdc4]/10"
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 rounded-lg bg-[#4ecdc4]/20 flex items-center justify-center">
                    <Wallet className="w-6 h-6 text-[#4ecdc4]" />
                  </div>
                  <div>
                    <div className="text-white/60 text-sm">USD 余额</div>
                    <div className="text-2xl font-bold text-white drop-shadow-[0_2px_2px_rgba(0,0,0,0.5)]">
                      {mockUserData.assets.balance.toFixed(2)}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2 mb-3">
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="w-2 h-2 rounded-full bg-[#4ecdc4]"
                  />
                  <span className="text-[#4ecdc4] text-sm">
                    +{mockUserData.assets.change24h} (24h)
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <motion.button
                    whileHover={{
                      scale: 1.05,
                      boxShadow: "0 0 15px rgba(96,239,255,0.3)",
                    }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleRecharge}
                    className="py-2 px-3 bg-gradient-to-r from-[#4ecdc4] to-[#f39c12] text-white rounded-lg text-sm font-medium shadow-lg shadow-[#4ecdc4]/30 hover:shadow-xl hover:shadow-[#4ecdc4]/40 transition-all"
                  >
                    充值
                  </motion.button>
                  <motion.button
                    whileHover={{
                      scale: 1.05,
                      boxShadow: "0 0 15px rgba(78,205,196,0.2)",
                    }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleWithdraw}
                    className="py-2 px-3 bg-white/10 text-[#4ecdc4] rounded-lg text-sm font-medium hover:bg-white/20 border border-[#4ecdc4]/20 transition-all"
                  >
                    提现
                  </motion.button>
                </div>
              </motion.div>
            </div>

            {/* 基础设置内容 */}

            {/* Account Connections */}
            <div className="relative mt-8">
              <AccountConnections
                onConnectionUpdate={handleConnectionUpdate}
                className="mt-8"
              />
            </div>
          </div>
        </motion.div>

        {/* Channel Section */}
        {youtubeData?.channelId ? (
          <ChannelSection channelId={youtubeData.channelId} />
        ) : (
          // Demo: Show a public channel when no user channel is connected
          <ChannelSection handle="@MrBeast" />
        )}

        {/* Achievements Section - Hidden for now */}
        <AchievementsSection
          achievements={mockAchievements}
          isVisible={false}
        />
      </div>
    </div>
  );
};

export default Profile;
